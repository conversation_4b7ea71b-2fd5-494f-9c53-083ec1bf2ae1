.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 750rpx;
  height: 2292rpx;
  overflow: hidden;
  .group_1 {
    width: 750rpx;
    height: 174rpx;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 64rpx;
      .text_1 {
        width: 64rpx;
        height: 36rpx;
                  overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 24rpx;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 14rpx 0 0 32rpx;
      }
      .thumbnail_1 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 502rpx;
      }
      .thumbnail_2 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 6rpx;
      }
      .thumbnail_3 {
        width: 38rpx;
        height: 38rpx;
        margin: 14rpx 30rpx 0 6rpx;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 108rpx;
      margin-bottom: 2rpx;
      .text_2 {
        width: 64rpx;
        height: 44rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 32rpx;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 44rpx;
        margin: 32rpx 0 0 24rpx;
      }
      .image_1 {
        width: 174rpx;
        height: 64rpx;
        margin: 22rpx 24rpx 0 0;
      }
    }
  }
  .group_2 {
    background-color: rgba(11, 206, 148, 1);
    height: 400rpx;
    margin-top: -2rpx;
    width: 750rpx;
    .group_3 {
      width: 746rpx;
      height: 400rpx;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG646d30f6a0c7e5fa5bec6d27bf576f5a.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin-left: 2rpx;
      .group_4 {
        width: 704rpx;
        height: 100rpx;
        margin: 72rpx 0 0 22rpx;
        .image-text_1 {
          width: 284rpx;
          height: 100rpx;
          .label_1 {
            width: 96rpx;
            height: 96rpx;
          }
          .group_5 {
            width: 160rpx;
            height: 92rpx;
            margin-top: 8rpx;
            .text-group_1 {
              width: 160rpx;
              height: 44rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 40rpx;
              font-family: Pinyon Script-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 44rpx;
            }
            .group_6 {
              background-color: rgba(255, 255, 255, 0.3);
              border-radius: 100px;
              width: 110rpx;
              height: 44rpx;
              margin-top: 4rpx;
              .block_1 {
                background-color: rgba(255, 255, 255, 1);
                width: 28rpx;
                height: 24rpx;
                margin: 12rpx 0 0 22rpx;
              }
              .text_3 {
                width: 16rpx;
                height: 26rpx;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                              font-size: 26rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26rpx;
              margin: 8rpx 30rpx 0 14rpx;
              }
            }
          }
        }
        .image_2 {
          width: 42rpx;
          height: 40rpx;
          margin-top: 28rpx;
        }
      }
      .thumbnail_4 {
        width: 18rpx;
        height: 28rpx;
        margin: 2rpx 0 0 422rpx;
      }
      .group_7 {
        background-image: linear-gradient(
          94deg,
          rgba(254, 217, 185, 1) 0,
          rgba(253, 240, 216, 1) 100%
        );
        border-radius: 10px 10px 0px 0px;
        height: 160rpx;
        width: 700rpx;
        margin: 26rpx 0 12rpx 24rpx;
        .group_8 {
          width: 700rpx;
          height: 160rpx;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG68d4f493265117f583f1a42c06f9d7e9.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-text_2 {
            width: 300rpx;
            height: 90rpx;
            margin: 16rpx 0 0 26rpx;
            .label_2 {
              width: 90rpx;
              height: 90rpx;
            }
            .text-group_2 {
              width: 192rpx;
              height: 32rpx;
              overflow-wrap: break-word;
              color: rgba(104, 57, 45, 0.7);
              font-size: 32rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 32rpx;
              margin-top: 30rpx;
            }
          }
          .text-wrapper_1 {
            background-color: rgba(151, 96, 58, 1);
            border-radius: 38px;
            height: 54rpx;
            width: 124rpx;
            margin: 32rpx 22rpx 0 228rpx;
            .text_4 {
              width: 98rpx;
              height: 22rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 22rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 22rpx;
              margin: 16rpx 0 0 14rpx;
            }
          }
        }
      }
    }
  }
  .group_9 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 404rpx;
    width: 702rpx;
    margin: 542rpx 0 0 24rpx;
    .box_3 {
      width: 634rpx;
      height: 96rpx;
      margin: 22rpx 0 0 46rpx;
      .image-text_3 {
        width: 52rpx;
        height: 94rpx;
        margin-top: 2rpx;
        .label_3 {
          width: 44rpx;
          height: 44rpx;
          margin-left: 8rpx;
        }
        .text-group_3 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 26rpx;
        }
      }
      .image-text_4 {
        width: 48rpx;
        height: 94rpx;
        margin: 2rpx 0 0 88rpx;
        .image_3 {
          width: 38rpx;
          height: 46rpx;
          margin-left: 8rpx;
        }
        .text-group_4 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 24rpx;
        }
      }
      .image-text_5 {
        width: 50rpx;
        height: 94rpx;
        margin: 2rpx 0 0 92rpx;
        .label_4 {
          width: 46rpx;
          height: 42rpx;
          margin-left: 4rpx;
        }
        .text-group_5 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
                     font-size: 24rpx;
           font-family: PingFang SC-Regular;
           font-weight: NaN;
           text-align: center;
           white-space: nowrap;
           line-height: 24rpx;
           margin-top: 28rpx;
        }
      }
      .image-text_6 {
        width: 48rpx;
        height: 96rpx;
        margin-left: 90rpx;
        .box_4 {
          height: 48rpx;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG6fd2d1a43c0e26482a7cea3a26996afa.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 48rpx;
          .image-wrapper_1 {
            background-color: rgba(75, 144, 248, 1);
            height: 46rpx;
            width: 46rpx;
            margin: 2rpx 0 0 2rpx;
            .label_5 {
              width: 46rpx;
              height: 46rpx;
            }
          }
        }
        .text-group_6 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 24rpx;
        }
      }
      .image-text_7 {
        width: 96rpx;
        height: 94rpx;
        margin: 2rpx 0 0 70rpx;
        .label_6 {
          width: 44rpx;
          height: 44rpx;
          margin-left: 30rpx;
        }
        .text-group_7 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 26rpx;
        }
      }
    }
    .box_5 {
      width: 658rpx;
      height: 90rpx;
      margin: 32rpx 0 0 22rpx;
      .image-text_8 {
        width: 96rpx;
        height: 90rpx;
        .image-wrapper_2 {
          height: 46rpx;
          background: url(/static/lanhu_gerenzhongxin2/4d1adf7e23c3453c99230214a2ba611d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 32rpx;
          width: 38rpx;
          .image_4 {
            width: 38rpx;
            height: 46rpx;
          }
        }
        .text-group_8 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 20rpx;
        }
      }
      .image-text_9 {
        width: 72rpx;
        height: 90rpx;
        margin-left: 56rpx;
        .label_7 {
          width: 44rpx;
          height: 44rpx;
          margin-left: 8rpx;
        }
        .text-group_9 {
          width: 72rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 22rpx;
        }
      }
      .image-text_10 {
        width: 72rpx;
        height: 88rpx;
        margin: 2rpx 0 0 68rpx;
        .image-wrapper_3 {
          height: 40rpx;
          background: url(/static/lanhu_gerenzhongxin2/1b7f2c88697d41d0818a0005068cd34d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 12rpx;
          width: 46rpx;
          .image_5 {
            width: 46rpx;
            height: 40rpx;
          }
        }
        .text-group_10 {
          width: 72rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 24rpx;
        }
      }
      .image-text_11 {
        width: 96rpx;
        height: 90rpx;
        margin-left: 56rpx;
        .image-wrapper_4 {
          height: 46rpx;
          background: url(/static/lanhu_gerenzhongxin2/7a0577e2c38241f396213507d929bb95_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 24rpx;
          width: 46rpx;
          .label_8 {
            width: 46rpx;
            height: 46rpx;
          }
        }
        .text-group_11 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 20rpx;
        }
      }
      .image-text_12 {
        width: 96rpx;
        height: 90rpx;
        margin-left: 46rpx;
        .image-wrapper_5 {
          height: 44rpx;
          background: url(/static/lanhu_gerenzhongxin2/4034f7295cd44c02a85a6c1b11a789f9_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 26rpx;
          width: 38rpx;
          .image_6 {
            width: 38rpx;
            height: 44rpx;
          }
        }
        .text-group_12 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 22rpx;
        }
      }
    }
    .box_6 {
      width: 516rpx;
      height: 90rpx;
      margin: 34rpx 0 40rpx 22rpx;
      .image-text_13 {
        width: 96rpx;
        height: 90rpx;
        .image-wrapper_6 {
          height: 44rpx;
          background: url(/static/lanhu_gerenzhongxin2/1ca55f70706842d69457e704f456f449_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 24rpx;
          width: 44rpx;
          .label_9 {
            width: 44rpx;
            height: 44rpx;
          }
        }
        .text-group_13 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 22rpx;
        }
      }
      .image-text_14 {
        width: 96rpx;
        height: 90rpx;
        margin-left: 44rpx;
        .image-wrapper_7 {
          height: 46rpx;
          background: url(/static/lanhu_gerenzhongxin2/a9797eee4ec44b468b37cefa99321402_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 24rpx;
          width: 44rpx;
          .label_10 {
            width: 44rpx;
            height: 46rpx;
          }
        }
        .text-group_14 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 20rpx;
        }
      }
      .image-text_15 {
        width: 120rpx;
        height: 90rpx;
        margin-left: 32rpx;
        .image-wrapper_8 {
          height: 46rpx;
          background: url(/static/lanhu_gerenzhongxin2/b2d7338e8fb843e1a1ab90ef7c472ae1_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 38rpx;
          width: 44rpx;
          .label_11 {
            width: 44rpx;
            height: 46rpx;
          }
        }
        .text-group_15 {
          width: 120rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 20rpx;
        }
      }
      .image-text_16 {
        width: 96rpx;
        height: 90rpx;
        margin-left: 32rpx;
        .image_7 {
          width: 36rpx;
          height: 44rpx;
          margin-left: 30rpx;
        }
        .text-group_16 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 22rpx;
        }
      }
    }
  }
  .group_10 {
    width: 750rpx;
    height: 624rpx;
    .group_11 {
      width: 700rpx;
      height: 40rpx;
      margin: 32rpx 0 0 24rpx;
              .text_5 {
          width: 128rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 32rpx;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin-top: 4rpx;
        }
              .text_6 {
          width: 128rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 32rpx;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin: 4rpx 0 0 56rpx;
        }
              .text_7 {
          width: 128rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 32rpx;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin: 4rpx 0 0 54rpx;
        }
      .image-text_17 {
        width: 102rpx;
        height: 40rpx;
        margin-left: 104rpx;
        .text-group_17 {
          width: 52rpx;
          height: 26rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26rpx;
          margin-top: 8rpx;
        }
        .thumbnail_5 {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
    .group_12 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 50px;
      width: 102rpx;
      height: 12rpx;
      margin: 6rpx 0 0 34rpx;
    }
    .list_1 {
      width: 702rpx;
      height: 484rpx;
      justify-content: space-between;
      margin: 20rpx 0 30rpx 24rpx;
      .list-items_1 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        width: 702rpx;
        height: 232rpx;
        margin-bottom: 20rpx;
        .image-text_18 {
          position: relative;
          width: 482rpx;
          height: 182rpx;
          margin: 24rpx 0 0 24rpx;
          .image_8 {
            width: 182rpx;
            height: 182rpx;
          }
          .text-group_18 {
            width: 276rpx;
            height: 174rpx;
            margin: 4rpx 0 0 22rpx;
            .text_8 {
              width: 180rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 30rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 30rpx;
            }
            .text_9 {
              width: 248rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(152, 152, 152, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin-top: 20rpx;
            }
            .box_7 {
              width: 276rpx;
              height: 36rpx;
              margin-top: 64rpx;
                             .text-wrapper_2 {
                 width: 82rpx;
                 height: 36rpx;
                 overflow-wrap: break-word;
                 font-size: 0;
                 font-family: PingFang SC-Medium;
                 font-weight: 500;
                 text-align: left;
                 line-height: 36rpx;
                                  .text_10 {
                   width: 82rpx;
                   height: 36rpx;
                   overflow-wrap: break-word;
                   color: rgba(231, 96, 81, 1);
                   font-size: 16rpx;
                   font-family: PingFang SC-Medium;
                   font-weight: 500;
                   text-align: left;
                   line-height: 16rpx;
                 }
                                 .text_11 {
                   width: 82rpx;
                   height: 36rpx;
                   overflow-wrap: break-word;
                   color: rgba(231, 96, 81, 1);
                   font-size: 36rpx;
                   font-family: PingFang SC-Medium;
                   font-weight: 500;
                   text-align: left;
                   white-space: nowrap;
                   line-height: 36rpx;
                 }
              }
                             .text_12 {
                 width: 104rpx;
                 height: 24rpx;
                 overflow-wrap: break-word;
                 color: rgba(153, 153, 153, 1);
                 font-size: 24rpx;
                 font-family: PingFang SC-Regular;
                 text-decoration: line-through;
                 font-weight: NaN;
                 text-align: left;
                 white-space: nowrap;
                 line-height: 24rpx;
                 margin-top: 6rpx;
               }
            }
          }
          .text-wrapper_3 {
            background-image: linear-gradient(
              90deg,
              rgba(255, 239, 190, 1) 0,
              rgba(254, 230, 157, 1) 100%
            );
            border-radius: 10px 10px 0px 10px;
            height: 30rpx;
            width: 72rpx;
            margin: 146rpx 0 0 -186rpx;
                         .text_13 {
               width: 48rpx;
               height: 16rpx;
               overflow-wrap: break-word;
               color: rgba(98, 59, 4, 1);
               font-size: 16rpx;
               font-family: PingFang SC-Regular;
               font-weight: NaN;
               text-align: left;
               white-space: nowrap;
               line-height: 16rpx;
               margin: 8rpx 0 0 12rpx;
             }
          }
          .block_2 {
            border-radius: 4px;
            width: 154rpx;
            height: 30rpx;
            border: 1px solid rgba(231, 96, 81, 1);
            margin: 92rpx 0 0 -38rpx;
            .group_13 {
              background-color: rgba(231, 96, 81, 1);
              border-radius: 4px 0px 4px 0px;
              height: 30rpx;
              width: 34rpx;
              .group_14 {
                background-color: rgba(255, 207, 202, 1);
                width: 16rpx;
                height: 20rpx;
                margin: 4rpx 0 0 10rpx;
              }
            }
                           .text_14 {
                 width: 106rpx;
                 height: 16rpx;
                 overflow-wrap: break-word;
                 color: rgba(231, 96, 81, 1);
                 font-size: 20rpx;
                 font-family: PingFang SC-Regular;
                 font-weight: NaN;
                 text-align: left;
                 white-space: nowrap;
                 line-height: 20rpx;
                 margin: 6rpx 6rpx 0 8rpx;
               }
          }
          .block_3 {
            background-image: linear-gradient(
              180deg,
              rgba(59, 41, 0, 1) 0,
              rgba(86, 54, 0, 1) 100%
            );
            border-radius: 4px;
            position: absolute;
            left: 204rpx;
            top: 92rpx;
            width: 112rpx;
            height: 30rpx;
            .image-text_19 {
              width: 90rpx;
              height: 20rpx;
              margin: 6rpx 0 0 12rpx;
              .block_4 {
                background-color: rgba(255, 234, 184, 1);
                width: 20rpx;
                height: 20rpx;
              }
                             .text-group_19 {
                 width: 64rpx;
                 height: 20rpx;
                 overflow-wrap: break-word;
                 color: rgba(255, 234, 184, 1);
                 font-size: 20rpx;
                 font-family: PingFang SC-Regular;
                 font-weight: NaN;
                 text-align: left;
                 white-space: nowrap;
                 line-height: 20rpx;
               }
            }
          }
        }
        .text-wrapper_4 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 60px;
          height: 50rpx;
          width: 132rpx;
          margin: 160rpx 24rpx 0 0;
                     .text_15 {
             width: 96rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(255, 255, 255, 1);
             font-size: 24rpx;
             font-family: PingFang SC-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 24rpx;
             margin: 14rpx 0 0 18rpx;
           }
        }
      }
    }
  }
  .group_15 {
    background-color: rgba(255, 255, 255, 1);
    width: 750rpx;
    height: 98rpx;
    margin: -2rpx 0 54rpx 0;
    .image-text_20 {
      width: 48rpx;
      height: 78rpx;
      margin-top: 12rpx;
      .label_12 {
        width: 44rpx;
        height: 44rpx;
        margin-left: 2rpx;
      }
             .text-group_20 {
         width: 48rpx;
         height: 24rpx;
         overflow-wrap: break-word;
         color: rgba(51, 51, 51, 1);
         font-size: 24rpx;
         font-family: PingFang SC-Regular;
         font-weight: NaN;
         text-align: center;
         white-space: nowrap;
         line-height: 24rpx;
         margin-top: 10rpx;
       }
    }
    .image-text_21 {
      width: 48rpx;
      height: 76rpx;
      margin-top: 14rpx;
      .label_13 {
        width: 44rpx;
        height: 44rpx;
        margin-left: 2rpx;
      }
             .text-group_21 {
         width: 48rpx;
         height: 24rpx;
         overflow-wrap: break-word;
         color: rgba(51, 51, 51, 1);
         font-size: 24rpx;
         font-family: PingFang SC-Regular;
         font-weight: NaN;
         text-align: center;
         white-space: nowrap;
         line-height: 24rpx;
         margin-top: 8rpx;
       }
    }
    .image-text_22 {
      width: 48rpx;
      height: 76rpx;
      margin-top: 14rpx;
      .label_14 {
        width: 44rpx;
        height: 44rpx;
        margin-left: 2rpx;
      }
             .text-group_22 {
         width: 48rpx;
         height: 24rpx;
         overflow-wrap: break-word;
         color: rgba(51, 51, 51, 1);
         font-size: 24rpx;
         font-family: PingFang SC-Regular;
         font-weight: NaN;
         text-align: center;
         white-space: nowrap;
         line-height: 24rpx;
         margin-top: 8rpx;
       }
    }
    .image-text_23 {
      width: 48rpx;
      height: 76rpx;
      margin-top: 14rpx;
      .label_15 {
        width: 44rpx;
        height: 44rpx;
        margin-left: 2rpx;
      }
             .text-group_23 {
         width: 48rpx;
         height: 24rpx;
         overflow-wrap: break-word;
         color: rgba(11, 206, 148, 1);
         font-size: 24rpx;
         font-family: PingFang SC-Regular;
         font-weight: NaN;
         text-align: center;
         white-space: nowrap;
         line-height: 24rpx;
         margin-top: 8rpx;
       }
    }
  }
  .group_16 {
    position: absolute;
    left: 0;
    top: 572rpx;
    width: 750rpx;
    height: 544rpx;
    .box_8 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 702rpx;
      height: 212rpx;
      margin: 164rpx 0 0 24rpx;
      .block_5 {
        width: 652rpx;
        height: 28rpx;
        margin: 32rpx 0 0 26rpx;
        .text_16 {
          width: 112rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 28rpx;
        }
        .text_17 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 2rpx 0 0 414rpx;
        }
        .thumbnail_6 {
          width: 10rpx;
          height: 16rpx;
          margin: 6rpx 0 0 20rpx;
        }
      }
      .block_6 {
        width: 652rpx;
        height: 90rpx;
        margin: 34rpx 0 28rpx 34rpx;
        .image-text_24 {
          width: 72rpx;
          height: 90rpx;
          .box_9 {
            height: 48rpx;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG3a152a939d0da18d03352ba14032f56e.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 12rpx;
            width: 48rpx;
            .text-wrapper_5 {
              background-color: rgba(238, 12, 12, 1);
              border-radius: 50%;
              height: 24rpx;
              width: 24rpx;
              margin: -4rpx 0 0 36rpx;
              .text_18 {
                width: 10rpx;
                height: 20rpx;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 20rpx;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 20rpx;
                margin: 2rpx 0 0 6rpx;
              }
            }
          }
                     .text-group_24 {
             width: 72rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(51, 51, 51, 1);
             font-size: 24rpx;
             font-family: Pinyon Script-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 24rpx;
             margin-top: 18rpx;
           }
        }
        .image-text_25 {
          width: 72rpx;
          height: 90rpx;
          margin-left: 68rpx;
          .label_16 {
            width: 48rpx;
            height: 50rpx;
            margin-left: 12rpx;
          }
                     .text-group_25 {
             width: 72rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(51, 51, 51, 1);
             font-size: 24rpx;
             font-family: Pinyon Script-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 24rpx;
             margin-top: 16rpx;
           }
        }
        .image-text_26 {
          width: 72rpx;
          height: 90rpx;
          margin-left: 68rpx;
          .label_17 {
            width: 48rpx;
            height: 48rpx;
            margin-left: 12rpx;
          }
                     .text-group_26 {
             width: 72rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(51, 51, 51, 1);
             font-size: 24rpx;
             font-family: Pinyon Script-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 24rpx;
             margin-top: 18rpx;
           }
        }
        .image-text_27 {
          width: 72rpx;
          height: 90rpx;
          margin-left: 68rpx;
          .label_18 {
            width: 48rpx;
            height: 48rpx;
            margin-left: 12rpx;
          }
                     .text-group_27 {
             width: 72rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(51, 51, 51, 1);
             font-size: 24rpx;
             font-family: Pinyon Script-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 24rpx;
             margin-top: 18rpx;
           }
        }
        .image-text_28 {
          width: 104rpx;
          height: 90rpx;
          margin-left: 56rpx;
          .label_19 {
            width: 48rpx;
            height: 50rpx;
            margin-left: 24rpx;
          }
                     .text-group_28 {
             width: 104rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(51, 51, 51, 1);
             font-size: 24rpx;
             font-family: Pinyon Script-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 24rpx;
             margin-top: 16rpx;
           }
        }
      }
    }
    .box_10 {
      width: 700rpx;
      height: 126rpx;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGfa2ef94064ba2c6d00469d8c83584628.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 20rpx 0 22rpx 24rpx;
      .image-text_29 {
        width: 366rpx;
        height: 78rpx;
        margin: 26rpx 0 0 34rpx;
        .group_17 {
          height: 78rpx;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG75681517c0627fcdab92e934834e4096.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 88rpx;
          .group_18 {
            height: 66rpx;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGe61461c9c7dc103306127b0493ef6139.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 78rpx;
            margin: 12rpx 0 0 -2rpx;
            .box_11 {
              width: 28rpx;
              height: 28rpx;
              background: url(/static/lanhu_gerenzhongxin2/6ae66c597d174fe6aa4c9f81b4de731f_mergeImage.png)
                100% no-repeat;
              background-size: 100% 100%;
              margin: 12rpx 0 0 26rpx;
            }
          }
        }
        .text-group_29 {
          width: 254rpx;
          height: 72rpx;
                   .text_19 {
           width: 252rpx;
           height: 36rpx;
           overflow-wrap: break-word;
           color: rgba(255, 255, 255, 1);
           font-size: 36rpx;
           font-family: PingFang SC-Medium;
           font-weight: 500;
           text-align: left;
           white-space: nowrap;
           line-height: 36rpx;
         }
                     .text_20 {
             width: 254rpx;
             height: 20rpx;
             overflow-wrap: break-word;
             color: rgba(255, 255, 255, 1);
             font-size: 20rpx;
             font-family: PingFang SC-Regular;
             font-weight: NaN;
             text-align: left;
             white-space: nowrap;
             line-height: 20rpx;
             margin-top: 16rpx;
           }
        }
      }
      .text-wrapper_6 {
        background-color: rgba(0, 143, 116, 1);
        border-radius: 38px;
        height: 54rpx;
        width: 124rpx;
        margin: 36rpx 24rpx 0 152rpx;
                 .text_21 {
           width: 98rpx;
           height: 22rpx;
           overflow-wrap: break-word;
           color: rgba(255, 255, 255, 1);
           font-size: 22rpx;
           font-family: PingFang SC-Regular;
           font-weight: NaN;
           text-align: center;
           white-space: nowrap;
           line-height: 22rpx;
           margin: 16rpx 0 0 14rpx;
         }
      }
    }
    .box_12 {
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      position: absolute;
      left: 24rpx;
      top: -56rpx;
      width: 702rpx;
      height: 200rpx;
      .box_13 {
        background-color: rgba(254, 247, 239, 1);
        border-radius: 10px;
        width: 306rpx;
        height: 142rpx;
        margin: 28rpx 0 0 26rpx;
        .image-text_30 {
          position: relative;
          width: 286rpx;
          height: 142rpx;
          margin-left: 20rpx;
          .text-group_30 {
            width: 176rpx;
            height: 92rpx;
            margin-top: 20rpx;
                       .text_22 {
             width: 96rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(34, 34, 34, 1);
             font-size: 24rpx;
             font-family: Inter-Medium;
             font-weight: 500;
             text-align: center;
             white-space: nowrap;
             line-height: 24rpx;
             margin-left: 36rpx;
           }
                       .text_23 {
             width: 176rpx;
             height: 48rpx;
             overflow-wrap: break-word;
             color: rgba(34, 34, 34, 1);
             font-size: 48rpx;
             font-family: PingFang SC-Regular;
             font-weight: NaN;
             text-align: center;
             white-space: nowrap;
             line-height: 48rpx;
             margin-top: 20rpx;
           }
          }
          .text-wrapper_7 {
            height: 142rpx;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG876225c7c7c7e608ee2a030b4a8f33b3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 104rpx;
                       .text_24 {
             width: 48rpx;
             height: 24rpx;
             overflow-wrap: break-word;
             color: rgba(255, 255, 255, 1);
             font-size: 24rpx;
             font-family: PingFang SC-Regular;
             font-weight: NaN;
             text-align: center;
             white-space: nowrap;
             line-height: 24rpx;
             margin: 60rpx 0 0 40rpx;
           }
          }
          .section_1 {
            background-color: rgba(231, 96, 81, 1);
            position: absolute;
            left: 0;
            top: 20rpx;
            width: 22rpx;
            height: 24rpx;
          }
        }
      }
      .box_14 {
        background-color: rgba(237, 249, 243, 1);
        border-radius: 10px;
        width: 306rpx;
        height: 142rpx;
        margin: 28rpx 36rpx 0 28rpx;
        .image-text_31 {
          width: 286rpx;
          height: 142rpx;
          margin-left: 20rpx;
          .text-group_31 {
            width: 156rpx;
            height: 92rpx;
            margin-top: 20rpx;
            .text_25 {
              width: 120rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 24rpx;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
              margin-left: 36rpx;
            }
                         .text-wrapper_8 {
               width: 60rpx;
               height: 48rpx;
               overflow-wrap: break-word;
               font-size: 0;
               font-family: PingFang SC-Regular;
               font-weight: NaN;
               text-align: center;
               white-space: nowrap;
               line-height: 48rpx;
               margin-top: 20rpx;
                              .text_26 {
                 width: 60rpx;
                 height: 48rpx;
                 overflow-wrap: break-word;
                 color: rgba(34, 34, 34, 1);
                 font-size: 48rpx;
                 font-family: PingFang SC-Regular;
                 font-weight: NaN;
                 text-align: center;
                 white-space: nowrap;
                 line-height: 48rpx;
               }
                             .text_27 {
                 width: 60rpx;
                 height: 48rpx;
                 overflow-wrap: break-word;
                 color: rgba(34, 34, 34, 1);
                 font-size: 30rpx;
                 font-family: PingFang SC-Regular;
                 font-weight: NaN;
                 text-align: center;
                 white-space: nowrap;
                 line-height: 30rpx;
               }
            }
          }
          .text-wrapper_9 {
            height: 142rpx;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGeafee3672b6f7f4ed5a9cff4ab408f2b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 104rpx;
                         .text_28 {
               width: 48rpx;
               height: 24rpx;
               overflow-wrap: break-word;
               color: rgba(255, 255, 255, 1);
               font-size: 24rpx;
               font-family: PingFang SC-Regular;
               font-weight: NaN;
               text-align: center;
               white-space: nowrap;
               line-height: 24rpx;
               margin: 58rpx 0 0 36rpx;
             }
          }
        }
        .thumbnail_7 {
          width: 28rpx;
          height: 28rpx;
          margin: 18rpx 256rpx 0 -284rpx;
        }
      }
    }
  }

  // ===== 项目列表相关样式 =====
  // tabs选项卡容器
  .group_7 {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    width: 702rpx;
    height: 50rpx;
    margin: 36rpx 0 0 36rpx;

    // tabs区域容器
    .tabs-container {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      flex: 1;
    }

    // 切换按钮样式
    .image-text_3 {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      width: 102rpx;
      height: 40rpx;
      margin-top: 6rpx;
      flex-shrink: 0;

      .text-group_4 {
        color: rgba(102, 102, 102, 1);
        font-size: 28rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
      }

      .thumbnail_4 {
        width: 32rpx;
        height: 32rpx;
      }

      &:active {
        opacity: 0.7;
      }
    }
  }

  .tab-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 128rpx;
    height: 50rpx;
    margin-right: 56rpx;

    &:last-of-type {
      margin-right: 0;
    }

    // 选项卡文字
    .tab-text {
      width: 128rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      color: rgba(153, 153, 153, 1);  // 默认灰色
      font-size: 32rpx;               // 默认字体大小
      font-family: PingFang SC-Medium;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      line-height: 32rpx;
      margin-top: 4rpx;
      transition: all 0.3s ease;      // 平滑过渡动画
      cursor: pointer;

      // 选中状态
      &.tab-active {
        color: rgba(34, 34, 34, 1);   // 选中时深色
      }
    }

    // 指示器
    .tab-indicator {
      position: absolute;
      bottom: 0;                      // 距离底部0
      left: 50%;
      transform: translateX(-50%);    // 水平居中
      width: 70rpx;
      height: 12rpx;                  // 增加高度
      background: linear-gradient(90deg, rgba(11, 206, 148, 1) 0%, rgba(11, 206, 148, 1) 100%);
      border-radius: 50px;
      // 添加指示器动画
      animation: slideIn 0.3s ease-out;
    }

    // 指示器滑入动画
    @keyframes slideIn {
      from {
        width: 0;
        opacity: 0;
      }
      to {
        width: 70rpx;
        opacity: 1;
      }
    }
  }

  // 技师列表栅格布局
  .technician-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 45rpx 24rpx 0 24rpx;
    gap: 24rpx;
    // 添加进入动画
    animation: fadeInUp 0.5s ease-out;
  }

  .technician-card {
    width: 340rpx;        // 固定宽度 170px * 2 = 340rpx
    min-width: 340rpx;    // 强制最小宽度
    max-width: 340rpx;    // 强制最大宽度
    flex: 0 0 340rpx;     // 不允许伸缩，固定基础尺寸
    margin-bottom: 22rpx;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 5px;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;
    // 为每个卡片添加延迟动画
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }

    // 技师头像背景区域
    .card-avatar {
      width: 100%;
      height: 286rpx;
      position: relative;
      overflow: hidden; // 确保图片不会溢出
      border-radius: 5px 5px 0px 0px; // 顶部圆角

      // 技师头像图片
      .technician-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover; // 保持图片比例并填充容器
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      // 时间标签
      .time-badge {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 8rpx;
        padding: 8rpx 12rpx;
        backdrop-filter: blur(4rpx);

        .time-label-wrapper {
          margin-bottom: 4rpx;
        }

        .time-label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 20rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
        }

        .time-value {
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
    }

    // 技师信息卡片内容
    .card-content {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      width: 100%;
      height: 286rpx;

      // 技师姓名和状态
      .technician-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 22rpx 0 22rpx;

        .technician-name {
          color: rgba(0, 0, 0, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: left;
          white-space: nowrap;
          line-height: 30rpx;
          flex: 1;
        }

        .status-badge {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          padding: 6rpx 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .status-text {
            color: rgba(255, 255, 255, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
          }
        }
      }

      // 评分和服务次数
      .rating-section {
        display: flex;
        align-items: center;
        padding: 16rpx 22rpx 0 22rpx;

        .rating-star {
          width: 22rpx;
          height: 22rpx;
          background: url(/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
          background-size: 100% 100%;
          margin-right: 8rpx;
        }

        .service-info {
          display: flex;
          align-items: center;
          gap: 16rpx;

          .rating-score {
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }

          .service-count {
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }

      // 出行费用
      .travel-fee {
        display: flex;
        align-items: center;
        padding: 12rpx 22rpx 0 22rpx;

        .fee-icon {
          width: 22rpx;
          height: 22rpx;
          margin-right: 8rpx;
        }

        .fee-text {
          color: rgba(11, 206, 148, 1);
          font-size: 20rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: left;
          white-space: nowrap;
          line-height: 20rpx;
        }
      }

      // 操作按钮
      .action-buttons {
        display: flex;
        justify-content: space-between;
        padding: 16rpx 22rpx 0 22rpx;
        gap: 12rpx;

        .btn-secondary {
          flex: 1;
          height: 52rpx;
          background-color: rgba(246, 246, 246, 1);
          border-radius: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .btn-text {
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }

        .btn-primary {
          flex: 1;
          height: 52rpx;
          background-color: rgba(11, 206, 148, 1);
          border-radius: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .btn-text {
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }

      // 底部图标信息
      .bottom-info {
        display: flex;
        justify-content: space-around;
        padding: 16rpx 22rpx 20rpx 22rpx;

        .info-item {
          display: flex;
          align-items: center;
          gap: 6rpx;

          .info-text {
            color: rgba(153, 153, 153, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 20rpx;
          }
        }
      }
    }
  }

  // 一行一列技师列表样式
  .technician-list-container {
    width: 702rpx;
    justify-content: space-between;
    margin: 45rpx 0 0 24rpx;
    // 添加进入动画
    animation: fadeInLeft 0.5s ease-out;

    .technician-list-item {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      height: 232rpx;
      margin-bottom: 20rpx;
      width: 702rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      overflow: hidden;
      animation: slideInFromLeft 0.6s ease-out both;

      // 为每个列表项添加延迟动画
      &:nth-child(1) { animation-delay: 0.1s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.3s; }
      &:nth-child(4) { animation-delay: 0.4s; }

      .technician-info-top {
        width: 654rpx;
        height: 124rpx;
        margin: 28rpx 0 0 24rpx;

        .technician-avatar-img {
          width: 124rpx;
          height: 124rpx;
          border-radius: 8rpx;
          object-fit: cover;
        }

        .single-row-image {
          width: 202rpx;
          height: 78rpx;
          margin-left: 24rpx;

          .technician-name-row {
            width: 202rpx;
            height: 32rpx;

            .technician-name-text {
              width: 92rpx;
              height: 32rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 30rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 30rpx;
            }

            .technician-photos-btn {
              background-color: rgba(11, 206, 148, 1);
              border-radius: 2px;
              height: 32rpx;
              width: 100rpx;

              .technician-photos-text {
                width: 80rpx;
                height: 20rpx;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 20rpx;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 20rpx;
                margin: 6rpx 0 0 10rpx;
              }
            }
          }

          .technician-rating-row {
            width: 192rpx;
            height: 24rpx;
            margin-top: 22rpx;

            .technician-rating-area {
              width: 46rpx;
              height: 24rpx;

              .technician-star-icon {
                width: 22rpx;
                height: 22rpx;
                background: url(/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
                background-size: 100% 100%;
                margin-top: 2rpx;
              }

              .technician-rating-text {
                width: 14rpx;
                height: 24rpx;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 24rpx;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 24rpx;
              }
            }

            .technician-service-text {
              width: 136rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }

        .single-row-time {
          width: 172rpx;
          height: 78rpx;
          margin-left: 132rpx;

          .technician-time-wrapper {
            background-color: rgba(62, 200, 174, 0.2);
            border-radius: 2px;
            height: 32rpx;
            width: 172rpx;

            .technician-time-text {
              width: 160rpx;
              height: 20rpx;
              overflow-wrap: break-word;
              color: rgba(62, 200, 174, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 20rpx;
              margin: 6rpx 0 0 6rpx;
            }
          }

          .technician-distance-area {
            width: 110rpx;
            height: 26rpx;
            margin: 20rpx 0 0 60rpx;

            .single-row-distance {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 22rpx;
              height: 26rpx;
            }

            .technician-distance-text {
              width: 84rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin-top: 2rpx;
            }
          }
        }
      }

      .technician-info-bottom {
        width: 642rpx;
        height: 52rpx;
        margin: 8rpx 0 20rpx 36rpx;
        position: relative;

        .technician-status-badge {
          border-radius: 100px;
          height: 32rpx;
          margin-top: 8rpx;
          min-width: 100rpx;
          padding: 0 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          left: 50rpx;
          transform: translateX(-50%);

          .technician-status-text {
            color: rgba(255, 255, 255, 1);
            font-size: 20rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
          }
        }

        .bottom-info {
          display: flex;
          align-items: center;
          gap: 30rpx;
          margin-left: 150rpx;
          margin-top: 14rpx;

          .info-item {
            display: flex;
            align-items: center;
            gap: 6rpx;

            .info-text {
              color: rgba(153, 153, 153, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
            }
          }
        }

        .technician-book-btn {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 30px;
          height: 52rpx;
          margin-left: 110rpx;
          width: 132rpx;

          .technician-book-text {
            width: 96rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 18rpx;
          }
        }
      }
    }
  }

  // Vue transition 动画类
  .fade-slide-enter-active,
  .fade-slide-leave-active {
    transition: all 0.4s ease-in-out;
  }

  .fade-slide-enter-from {
    opacity: 0;
    transform: translateX(30rpx);
  }

  .fade-slide-leave-to {
    opacity: 0;
    transform: translateX(-30rpx);
  }

  // 动画关键帧
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-30rpx);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-50rpx) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }

  // 到店服务栅格布局样式
  .shop-grid {
    display: flex;
    flex-wrap: wrap;
    width: 702rpx;
    margin-left: 24rpx;
    gap: 22rpx;
    justify-content: space-between;
    margin-top: 45rpx;
    // 添加进入动画
    animation: fadeInUp 0.5s ease-out;

    // 当只有一个元素时居中显示
    &:has(.shop-item:only-child) {
      justify-content: center;
    }

    // 当有两个元素时两端对齐
    &:has(.shop-item:nth-child(2):last-child) {
      justify-content: space-between;
    }
  }

  .shop-item {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 5px;
    width: 340rpx;
    height: auto;
    flex: 0 0 340rpx;
    margin-bottom: 20rpx;
    animation: slideInFromLeft 0.6s ease-out both;

    // 为每个店铺项添加延迟动画
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .shop-image {
      width: 340rpx;
      height: 360rpx;
      object-fit: cover;
      border-radius: 5px 5px 0 0;
      display: block;
    }

    .shop-name {
      width: 300rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 30rpx;
      font-family: Source Han Sans CN-Regular;
      font-weight: NaN;
      text-align: left;
      white-space: nowrap;
      line-height: 30rpx;
      margin: 20rpx 0 0 20rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .rating-section {
      display: flex;
      align-items: center;
      margin: 12rpx 0 0 20rpx;
      gap: 8rpx;

      .star-bg {
        width: 22rpx;
        height: 22rpx;
        background: url(/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
        background-size: 100% 100%;
      }

      .rating-text {
        color: rgba(102, 102, 102, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
      }

      .star-icon {
        width: 24rpx;
        height: 24rpx;
        margin-left: 8rpx;
      }

      .hours-text {
        color: rgba(102, 102, 102, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
        margin-left: 8rpx;
      }
    }

    .distance-section {
      display: flex;
      align-items: center;
      margin: 12rpx 0 0 20rpx;
      gap: 8rpx;

      .location-icon {
        width: 22rpx;
        height: 22rpx;
      }

      .distance-text {
        color: rgba(102, 102, 102, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
      }
    }

    .status-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12rpx 20rpx 0 20rpx;

      .status-wrapper {
        border: 0.5px solid rgba(11, 206, 148, 1);
        border-radius: 2px;
        padding: 6rpx 12rpx;

        .status-text {
          color: rgba(11, 206, 148, 1);
          font-size: 20rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
        }
      }

      .service-wrapper {
        background-color: rgba(246, 168, 78, 0.2);
        border-radius: 2px;
        padding: 6rpx 12rpx;

        .service-text {
          color: rgba(247, 140, 53, 1);
          font-size: 20rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
        }
      }
    }

    .rating-info-wrapper {
      margin: 12rpx 20rpx 20rpx 20rpx;
      background-color: rgba(62, 200, 174, 0.2);
      border-radius: 3px;
      padding: 6rpx 12rpx;

      .rating-info-text {
        color: rgba(11, 206, 148, 1);
        font-size: 20rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: left;
        white-space: nowrap;
        line-height: 20rpx;
      }
    }
  }

  // 到店服务一行一列布局样式
  .shop-service-list {
    width: 702rpx;
    height: auto;
    justify-content: space-between;
    margin: 50rpx 0 0 24rpx;
    // 添加进入动画
    animation: fadeInLeft 0.5s ease-out;

    .shop-service-item {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 702rpx;
      height: 180rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      overflow: hidden;
      animation: slideInFromLeft 0.6s ease-out both;

      // 为每个店铺项添加延迟动画
      &:nth-child(1) { animation-delay: 0.1s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.3s; }
      &:nth-child(4) { animation-delay: 0.4s; }

      .shop-service-main {
        width: 390rpx;
        height: 124rpx;
        margin: 28rpx 0 0 24rpx;

        .shop-service-image {
          width: 124rpx;
          height: 124rpx;
          border-radius: 8rpx;
          object-fit: cover;
        }

        .shop-service-info {
          width: 242rpx;
          height: 118rpx;

          .shop-service-header {
            width: 242rpx;
            height: 74rpx;

            .shop-service-name {
              width: 242rpx;
              height: 32rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 30rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 30rpx;
              text-overflow: ellipsis;
              overflow: hidden;
            }

            .shop-service-rating-time {
              width: 188rpx;
              height: 24rpx;
              margin: 18rpx 0 0 32rpx;

              .shop-service-rating {
                width: 14rpx;
                height: 24rpx;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 24rpx;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 24rpx;
              }

              .shop-service-hours {
                width: 130rpx;
                height: 24rpx;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 24rpx;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 24rpx;
              }
            }
          }

          .shop-service-divider {
            width: 22rpx;
            height: 22rpx;
            background: url(/static/lanhu_mendianfuwu/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
            background-size: 100% 100%;
            margin-top: -22rpx;
          }

          .shop-service-star {
            width: 24rpx;
            height: 24rpx;
            margin: -22rpx 0 0 58rpx;
          }

          .shop-service-rating-info {
            background-color: rgba(62, 200, 174, 0.2);
            border-radius: 3px;
            height: 26rpx;
            margin-top: 16rpx;
            width: 226rpx;

            .shop-service-rating-text {
              width: 218rpx;
              height: 20rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 20rpx;
              margin: 3rpx 0 0 4rpx;
            }
          }
        }
      }

      .shop-service-status {
        border-radius: 2px;
        height: 32rpx;
        border: 0.5px solid rgba(11, 206, 148, 1);
        width: 84rpx;
        margin: 28rpx 0 0 -12rpx;

        .shop-service-status-text {
          width: 60rpx;
          height: 20rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 20rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
          margin: 6rpx 0 0 12rpx;
        }
      }

      .shop-service-details {
        width: 112rpx;
        height: 118rpx;
        margin: 28rpx 24rpx 0 80rpx;

        .shop-service-count {
          background-color: rgba(246, 168, 78, 0.2);
          border-radius: 2px;
          height: 32rpx;
          margin-left: 8rpx;
          width: 104rpx;

          .shop-service-count-text {
            width: 82rpx;
            height: 20rpx;
            overflow-wrap: break-word;
            color: rgba(247, 140, 53, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
            margin: 6rpx 0 0 12rpx;
          }
        }

        .shop-service-distance {
          width: 110rpx;
          height: 26rpx;
          margin-top: 60rpx;

          .shop-service-location {
            background-color: rgba(11, 206, 148, 1);
            width: 22rpx;
            height: 26rpx;
          }

          .shop-service-distance-text {
            width: 84rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 2rpx;
          }
        }
      }
    }
  }

  // 预约上门列表样式
  .list_1 {
    width: 702rpx;
    height: auto;
    justify-content: space-between;
    margin: 45rpx 0 0 24rpx;
    // 添加预约上门列表进入动画
    animation: fadeInUp 0.5s ease-out;

    .list-items_1 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 702rpx;
      height: 232rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      overflow: hidden;
      animation: slideInFromLeft 0.6s ease-out both;

      // 为每个列表项添加延迟动画
      &:nth-child(1) { animation-delay: 0.1s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.3s; }
      &:nth-child(4) { animation-delay: 0.4s; }

      .image-text_4 {
        width: 654rpx;
        height: 124rpx;
        margin: 28rpx 0 0 24rpx;

        .image_2 {
          width: 124rpx;
          height: 124rpx;
          border-radius: 8rpx;
          object-fit: cover;
        }

        .text-group_5 {
          width: 202rpx;
          height: 78rpx;
          margin-left: 24rpx;

          .text_15 {
            width: 202rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 30rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
          }

          .text_16 {
            width: 202rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 8rpx;
          }

          .section_2 {
            width: 202rpx;
            height: 24rpx;
            margin-top: 14rpx;

            .text-wrapper_3 {
              display: flex;
              align-items: baseline;

              .text_17 {
                color: rgba(255, 102, 102, 1);
                font-size: 20rpx;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 20rpx;
              }

              .text_18 {
                color: rgba(255, 102, 102, 1);
                font-size: 30rpx;
                font-family: Source Han Sans CN-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 30rpx;
              }
            }

            .text_19 {
              color: rgba(153, 153, 153, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              text-decoration: line-through;
            }
          }
        }

        .text-wrapper_4 {
          background-color: rgba(62, 200, 174, 0.2);
          border-radius: 2px;
          height: 32rpx;
          width: 100rpx;
          margin-left: 24rpx;

          .text_20 {
            width: 80rpx;
            height: 20rpx;
            overflow-wrap: break-word;
            color: rgba(62, 200, 174, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
            margin: 6rpx 0 0 10rpx;
          }
        }

        .box_4 {
          width: 132rpx;
          height: 26rpx;
          margin: 50rpx 0 0 24rpx;

          .block_5 {
            width: 22rpx;
            height: 22rpx;

            .section_3 {
              width: 22rpx;
              height: 22rpx;
              background: url(/static/userhome/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
              background-size: 100% 100%;
            }
          }

          .text_21 {
            width: 106rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 2rpx;
          }
        }

        .box_5 {
          width: 110rpx;
          height: 26rpx;
          margin: 50rpx 0 0 24rpx;

          .image-text_5 {
            width: 110rpx;
            height: 26rpx;

            .group_9 {
              width: 22rpx;
              height: 22rpx;
              background-color: rgba(11, 206, 148, 1);
              border-radius: 50px;
            }

            .text-group_6 {
              width: 84rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin-top: 2rpx;
            }
          }
        }
      }

      .text-wrapper_5 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 30px;
        height: 52rpx;
        margin: 28rpx 24rpx 0 0;
        width: 132rpx;

        .text_22 {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 14rpx 0 0 18rpx;
        }
      }
    }
  }
}
